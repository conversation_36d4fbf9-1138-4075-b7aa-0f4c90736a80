import { QueryClient, QueryFunction } from "@tanstack/react-query";
import { validate, ValidationError } from "jsonschema";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

interface ApiRequestOptions {
  rateLimit?: {
    enabled: boolean;
    requestsPerMinute: number;
  };
  timeout?: {
    enabled: boolean;
    milliseconds: number;
  };
  validation?: {
    enabled: boolean;
    schema: Record<string, any>;
  };
  format?: 'json' | 'text' | 'binary';
  auth?: {
    type: 'apiKey';
    header: string;
    key: string;
  };
}

interface ApiError {
  error: string;
  details?: unknown;
  code?: string;
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown,
  options?: ApiRequestOptions
): Promise<Response> {
  try {
    // Validate request data if validation is enabled
    if (options?.validation?.enabled) {
      const validation = validate(data, options.validation.schema);
      if (!validation.valid) {
        throw new Error(`Request validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
      }
    }

    // Prepare headers
    const headers: Record<string, string> = {};
    if (data) {
      headers['Content-Type'] = 'application/json';
    }

    // Add authentication header if provided
    if (options?.auth?.type === 'apiKey') {
      headers[options.auth.header] = options.auth.key;
    }

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
      credentials: 'include',
    };

    // Add timeout if enabled
    if (options?.timeout?.enabled) {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), options.timeout.milliseconds);
      fetchOptions.signal = controller.signal;
    }

    // Make the request
    const res = await fetch(url, fetchOptions);

    // Handle rate limiting
    if (res.status === 429 && options?.rateLimit?.enabled) {
      const retryAfter = res.headers.get('Retry-After');
      throw new Error(`Rate limit exceeded. Please try again after ${retryAfter || 'some time'}`);
    }

    // Handle other errors
    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      throw new Error(errorData.error || `Request failed with status ${res.status}`);
    }

    return res;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`API request failed: ${error.message}`);
    }
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
