# Testing Enhanced Webhook Data Flow

This document provides comprehensive test cases for the enhanced webhook trigger system with improved data flow and validation.

## Test Setup

### 1. Create Test Workflow

Create a workflow with the following nodes:

1. **API Trigger Node** (Entry point)
   - Allowed Methods: POST, GET
   - Request Validation: Enabled
   - Validation Schema: Form submission schema

2. **Prompt Node** (Data processing)
   - Template with webhook variables
   - Input validation enabled

3. **Agent Node** (AI processing)
   - System prompt for webhook analysis
   - Custom prompt template

### 2. API Trigger Node Configuration

```json
{
  "name": "Webhook Receiver",
  "allowedMethods": ["POST", "GET"],
  "authType": "none",
  "requestValidation": {
    "enabled": true,
    "schema": {
      "type": "object",
      "properties": {
        "name": { "type": "string", "minLength": 1 },
        "email": { "type": "string", "format": "email" },
        "message": { "type": "string", "minLength": 1 }
      },
      "required": ["name", "email"]
    }
  },
  "responseConfig": {
    "successStatus": 200,
    "successMessage": "Webhook processed successfully"
  }
}
```

### 3. Prompt Node Configuration

```json
{
  "name": "Process Webhook Data",
  "prompt": "You received a {{webhook.method}} request.\n\nUser Details:\n- Name: {{body.name}}\n- Email: {{body.email}}\n- Message: {{body.message}}\n\nRequest Headers:\n{{webhook.headers_json}}\n\nPlease create a personalized response for {{body.name}}.",
  "provider": "gemini",
  "model": "gemini-2.0-flash",
  "maxTokens": 500,
  "temperature": 0.7,
  "outputFormat": "text",
  "inputValidation": {
    "enabled": true,
    "schema": {
      "type": "object",
      "properties": {
        "method": { "type": "string" },
        "body": {
          "type": "object",
          "properties": {
            "name": { "type": "string" },
            "email": { "type": "string" }
          },
          "required": ["name", "email"]
        }
      },
      "required": ["method", "body"]
    }
  }
}
```

### 4. Agent Node Configuration

```json
{
  "name": "Analyze Webhook",
  "systemPrompt": "You are a webhook analysis assistant. Analyze incoming webhook data and provide insights about the request, potential security considerations, and recommended actions.",
  "provider": "gemini",
  "model": "gemini-2.0-flash",
  "maxTokens": 800,
  "temperature": 0.5,
  "outputFormat": "json",
  "promptTemplate": "Analyze this webhook request for security implications and data quality. Provide recommendations for handling this type of request.",
  "inputValidation": {
    "enabled": true,
    "schema": {
      "type": "object",
      "properties": {
        "method": { "type": "string" },
        "headers": { "type": "object" },
        "body": { "type": "object" }
      },
      "required": ["method"]
    }
  },
  "schema": {
    "type": "object",
    "properties": {
      "security_assessment": { "type": "string" },
      "data_quality": { "type": "string" },
      "recommendations": {
        "type": "array",
        "items": { "type": "string" }
      },
      "risk_level": { "type": "string", "enum": ["low", "medium", "high"] }
    }
  }
}
```

## Test Cases

### Test Case 1: Valid POST Request

**Request:**
```bash
curl -X POST "http://localhost:3000/api/trigger/1/api-trigger-node" \
  -H "Content-Type: application/json" \
  -H "User-Agent: TestClient/1.0" \
  -d '{
    "name": "Alice Johnson",
    "email": "<EMAIL>",
    "message": "Hello, I would like to inquire about your services."
  }'
```

**Expected Flow:**
1. API Trigger validates the request body against the schema
2. Creates structured webhook data with method, headers, body, etc.
3. Prompt Node processes template variables:
   - `{{webhook.method}}` → "POST"
   - `{{body.name}}` → "Alice Johnson"
   - `{{body.email}}` → "<EMAIL>"
   - `{{body.message}}` → "Hello, I would like to inquire about your services."
4. Agent Node receives structured prompt about webhook analysis
5. Returns JSON response with security assessment

**Expected Response:**
```json
{
  "success": true,
  "runId": 123,
  "message": "Webhook processed successfully"
}
```

### Test Case 2: GET Request with Query Parameters

**Request:**
```bash
curl -X GET "http://localhost:3000/api/trigger/1/api-trigger-node?name=Bob%20Smith&email=<EMAIL>&action=subscribe"
```

**Expected Flow:**
1. API Trigger validates query parameters (since it's a GET request)
2. Template variables access query data:
   - `{{query.name}}` → "Bob Smith"
   - `{{query.email}}` → "<EMAIL>"
   - `{{query.action}}` → "subscribe"

### Test Case 3: Invalid Data (Validation Failure)

**Request:**
```bash
curl -X POST "http://localhost:3000/api/trigger/1/api-trigger-node" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "email": "invalid-email",
    "message": "Test message"
  }'
```

**Expected Response:**
```json
{
  "success": false,
  "error": "Request validation failed",
  "details": [
    "instance.name does not meet minimum length of 1",
    "instance.email does not conform to the \"email\" format"
  ]
}
```

### Test Case 4: Missing Template Variables

**Scenario:** Prompt template references `{{body.phone}}` but request doesn't include phone field.

**Expected Behavior:**
1. Validation warning logged about missing variable
2. Template variable replaced with `[body.phone: not found]`
3. Workflow continues execution

### Test Case 5: Complex Nested Data

**Request:**
```bash
curl -X POST "http://localhost:3000/api/trigger/1/api-trigger-node" \
  -H "Content-Type: application/json" \
  -d '{
    "user": {
      "profile": {
        "name": "Charlie Brown",
        "email": "<EMAIL>"
      },
      "preferences": {
        "notifications": true,
        "theme": "dark"
      }
    },
    "metadata": {
      "source": "website",
      "campaign": "summer2024"
    }
  }'
```

**Template Variables:**
- `{{body.user.profile.name}}` → "Charlie Brown"
- `{{body.user.profile.email}}` → "<EMAIL>"
- `{{body.user.preferences.theme}}` → "dark"
- `{{body.metadata.source}}` → "website"

## Validation Test Cases

### Test Case 6: Prompt Node Input Validation

**Scenario:** Prompt node expects webhook data but receives malformed input.

**Input to Prompt Node:**
```json
{
  "method": "POST",
  "body": null
}
```

**Expected Result:** Validation error thrown, workflow stops.

### Test Case 7: Agent Node Input Validation

**Scenario:** Agent node expects minimum required fields.

**Input to Agent Node:**
```json
{
  "headers": {},
  "body": {}
}
```

**Expected Result:** Validation error for missing "method" field.

## Helper Function Tests

### Test Case 8: Webhook Helper Functions

**Template:**
```
Method: {{webhook.method}}
All Data: {{webhook.all_data}}
Body JSON: {{webhook.body_json}}
```

**Expected Output:**
```
Method: POST
All Data: {
  "method": "POST",
  "headers": {...},
  "body": {...},
  ...
}
Body JSON: {
  "name": "Alice Johnson",
  "email": "<EMAIL>",
  "message": "Hello, I would like to inquire about your services."
}
```

### Test Case 9: JSON Helper Functions

**Template:**
```
User Data: {{json.body.user}}
Query Params: {{json.query}}
```

**Expected Output:**
```
User Data: {
  "profile": {
    "name": "Charlie Brown",
    "email": "<EMAIL>"
  }
}
Query Params: {
  "action": "subscribe"
}
```

## Performance Tests

### Test Case 10: Large Payload

**Request:** Send a 1MB JSON payload to test performance and memory usage.

**Expected Behavior:**
- Request processed successfully
- Template variables extracted correctly
- No memory leaks or performance degradation

### Test Case 11: High Frequency Requests

**Scenario:** Send 100 requests per minute to test rate limiting and concurrent processing.

**Expected Behavior:**
- Rate limiting applied if configured
- All valid requests processed
- No data corruption between concurrent requests

## Debugging and Logging

### Log Levels to Check

1. **Debug Level:**
   - Template variable extraction
   - Available vs. missing variables
   - Input data structure analysis

2. **Info Level:**
   - Workflow execution progress
   - Node completion status

3. **Warning Level:**
   - Missing template variables
   - Validation warnings

4. **Error Level:**
   - Validation failures
   - Node execution errors

### Example Log Output

```
[DEBUG] PromptNodeExecutor: Missing template variables in prompt
{
  "missingVariables": ["body.phone"],
  "availableKeys": ["method", "headers", "body", "body.name", "body.email", "body.message"]
}

[DEBUG] AgentNodeExecutor: Generated prompt
{
  "inputStructure": {
    "method": "string",
    "headers": "object",
    "body": "object",
    "query": "object"
  },
  "promptLength": 245,
  "systemPromptLength": 156
}
```

## Success Criteria

✅ **API Trigger Node:**
- Correctly validates requests based on HTTP method
- Passes structured data to downstream nodes
- Handles authentication and rate limiting

✅ **Prompt Node:**
- Supports nested template variable access
- Provides helpful webhook and JSON helper functions
- Validates input data when configured
- Logs missing variables without failing

✅ **Agent Node:**
- Intelligently processes webhook vs. generic data
- Creates structured prompts for AI processing
- Supports custom prompt templates
- Validates input data when configured

✅ **Error Handling:**
- Clear validation error messages
- Non-blocking missing variable handling
- Comprehensive logging for debugging

✅ **Performance:**
- Handles large payloads efficiently
- Supports concurrent request processing
- No memory leaks or performance degradation
