// Shared utility functions for node executors

export class NodeExecutorUtils {
  /**
   * Get the structure of an input object for debugging purposes
   */
  static getInputStructure(obj: any, depth = 0): any {
    if (depth > 3) return '[...deep object...]';

    if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
      const structure: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (value && typeof value === 'object') {
          structure[key] = Array.isArray(value) ? '[array]' : this.getInputStructure(value, depth + 1);
        } else {
          structure[key] = typeof value;
        }
      }
      return structure;
    }

    return typeof obj;
  }

  /**
   * Get all available keys from a nested object (for debugging template variables)
   */
  static getAvailableKeys(obj: any, prefix = ''): string[] {
    const keys: string[] = [];

    if (obj && typeof obj === 'object') {
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        keys.push(fullKey);

        if (value && typeof value === 'object' && !Array.isArray(value)) {
          keys.push(...this.getAvailableKeys(value, fullKey));
        }
      }
    }

    return keys;
  }

  /**
   * Get nested value from object using dot notation
   */
  static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' ? current[key] : undefined;
    }, obj);
  }

  /**
   * Check if input data appears to be webhook data
   */
  static isWebhookData(input: any): boolean {
    return input && typeof input === 'object' &&
           ('method' in input || 'headers' in input || 'body' in input);
  }

  /**
   * Filter headers to only include relevant ones for webhook processing
   */
  static filterRelevantHeaders(headers: any): any {
    const relevantHeaderKeys = [
      'content-type',
      'user-agent',
      'authorization',
      'x-forwarded-for',
      'x-real-ip',
      'x-api-key',
      'x-webhook-signature',
      'x-github-event',
      'x-slack-signature',
      'x-hub-signature',
      'x-hub-signature-256'
    ];

    const filtered: any = {};
    if (headers && typeof headers === 'object') {
      for (const [key, value] of Object.entries(headers)) {
        if (relevantHeaderKeys.includes(key.toLowerCase())) {
          filtered[key] = value;
        }
      }
    }

    return filtered;
  }

  /**
   * Create a structured prompt section for webhook data
   */
  static createWebhookDataSection(input: any): string[] {
    const sections: string[] = [];

    // HTTP Method
    if (input.method) {
      sections.push(`HTTP Method: ${input.method}`);
    }

    // Request Body
    if (input.body && Object.keys(input.body).length > 0) {
      sections.push(`Request Body:\n${JSON.stringify(input.body, null, 2)}`);
    }

    // Query Parameters
    if (input.query && Object.keys(input.query).length > 0) {
      sections.push(`Query Parameters:\n${JSON.stringify(input.query, null, 2)}`);
    }

    // Headers (filtered for relevant ones)
    if (input.headers) {
      const relevantHeaders = this.filterRelevantHeaders(input.headers);
      if (Object.keys(relevantHeaders).length > 0) {
        sections.push(`Relevant Headers:\n${JSON.stringify(relevantHeaders, null, 2)}`);
      }
    }

    // URL Parameters
    if (input.params && Object.keys(input.params).length > 0) {
      sections.push(`URL Parameters:\n${JSON.stringify(input.params, null, 2)}`);
    }

    // Validation info
    if (input.validation?.enabled) {
      sections.push(`Validation: ${input.validation.validationTarget} was validated successfully`);
    }

    return sections;
  }

  /**
   * Handle webhook helper functions for template replacement
   */
  static handleWebhookHelper(path: string, input: any): string {
    const helperPath = path.substring(8); // Remove 'webhook.'

    switch (helperPath) {
      case 'method':
        return input.method || '[method: not found]';
      case 'body_json':
        return input.body ? JSON.stringify(input.body, null, 2) : '[body: empty]';
      case 'query_params':
        return input.query ? JSON.stringify(input.query, null, 2) : '[query: empty]';
      case 'headers_json':
        return input.headers ? JSON.stringify(input.headers, null, 2) : '[headers: empty]';
      case 'relevant_headers_json':
        const relevantHeaders = this.filterRelevantHeaders(input.headers);
        return Object.keys(relevantHeaders).length > 0
          ? JSON.stringify(relevantHeaders, null, 2)
          : '[relevant headers: none]';
      case 'all_data':
        return JSON.stringify(input, null, 2);
      default:
        return `[webhook.${helperPath}: unknown helper]`;
    }
  }

  /**
   * Handle JSON helper functions for template replacement
   */
  static handleJsonHelper(path: string, input: any): string {
    const jsonPath = path.substring(5); // Remove 'json.'
    const value = this.getNestedValue(input, jsonPath);

    if (value === undefined || value === null) {
      return `[${jsonPath}: not found]`;
    }

    return JSON.stringify(value, null, 2);
  }

  /**
   * Validate that required template variables are available
   */
  static validateTemplateVariables(template: string, input: any): string[] {
    const missingVariables: string[] = [];
    const templateRegex = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = templateRegex.exec(template)) !== null) {
      const variable = match[1].trim();

      // Skip helper functions
      if (variable.startsWith('webhook.') || variable.startsWith('json.')) {
        continue;
      }

      // Check if variable exists in data
      if (this.getNestedValue(input, variable) === undefined) {
        missingVariables.push(variable);
      }
    }

    return missingVariables;
  }

  /**
   * Replace template variables with enhanced support for nested objects and helpers
   */
  static replaceTemplateVariables(template: string, input: any, onMissingVariable?: (variable: string) => void): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (_, path) => {
      const trimmedPath = path.trim();

      // Handle special helper functions
      if (trimmedPath.startsWith('webhook.')) {
        return this.handleWebhookHelper(trimmedPath, input);
      }

      if (trimmedPath.startsWith('json.')) {
        return this.handleJsonHelper(trimmedPath, input);
      }

      // Handle nested object access (e.g., body.name, query.id)
      const value = this.getNestedValue(input, trimmedPath);

      if (value === undefined || value === null) {
        if (onMissingVariable) {
          onMissingVariable(trimmedPath);
        }
        return `[${trimmedPath}: not found]`;
      }

      return String(value);
    });
  }
}
