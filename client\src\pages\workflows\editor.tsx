import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useLocation } from 'wouter';
import TopNavigation from '@/components/layout/TopNavigation';
import WorkflowBuilder from '@/components/workflows/WorkflowBuilder';
import WorkflowRunHistory from '@/components/workflows/WorkflowRunHistory';
import RunWorkflowModal from '@/components/workflows/modals/RunWorkflowModal';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Workflow } from '@/types/workflow';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

const WorkflowEditor: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // State for workflow data
  const [workflow, setWorkflow] = useState<Workflow | null>(null);
  const [isRunModalOpen, setIsRunModalOpen] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [selectedHistoryRunId, setSelectedHistoryRunId] = useState<number | undefined>(undefined);

  // Fetch workflow if ID is provided
  const { data: fetchedWorkflow, isLoading, error } = useQuery<Workflow>({
    queryKey: [`/api/workflows/${id}`],
    enabled: !!id,
  });

  // Create new workflow mutation
  const createWorkflowMutation = useMutation({
    mutationFn: async (data: Partial<Workflow>) => {
      return apiRequest('POST', '/api/workflows', data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
      setLocation(`/workflows/editor/${data.id}`);
      toast({
        title: "Workflow created",
        description: "Your new workflow has been created successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to create workflow",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async (workflowId: number) => {
      return apiRequest('POST', `/api/workflows/${workflowId}/favorite`, {});
    },
    onSuccess: (updatedWorkflow) => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
      queryClient.invalidateQueries({ queryKey: [`/api/workflows/${id}`] });
      setWorkflow(prev => prev ? { ...prev, isFavorite: !prev.isFavorite } : null);
      toast({
        title: updatedWorkflow.isFavorite ? "Added to favorites" : "Removed from favorites",
        description: `Workflow has been ${updatedWorkflow.isFavorite ? 'added to' : 'removed from'} favorites`,
      });
    }
  });

  // Initialize with default workflow if creating new
  useEffect(() => {
    if (!id && !workflow && !isLoading) {
      // Create a new workflow if no ID is provided
      const newWorkflow: Partial<Workflow> = {
        name: 'New Workflow',
        description: 'A workflow created with AI Workflow Builder',
        status: 'draft',
        isFavorite: false,
        nodes: {},
        edges: {}
      };

      createWorkflowMutation.mutate(newWorkflow);
    }
  }, [id, isLoading]);

  // Update local workflow state when data is fetched
  useEffect(() => {
    if (fetchedWorkflow) {
      setWorkflow(fetchedWorkflow);
    }
  }, [fetchedWorkflow]);

  // Handle workflow save
  const handleSave = (updatedWorkflow: Partial<Workflow>) => {
    if (workflow) {
      setWorkflow({ ...workflow, ...updatedWorkflow });
    }
  };

  // Handle workflow run from TopNavigation - opens the run modal
  const handleRunWorkflowFromNav = useCallback(() => {
    setIsRunModalOpen(true);
  }, []);

  // Handle workflow run completion - this will be passed to WorkflowBuilder and RunWorkflowModal
  const handleRunWorkflowComplete = useCallback(() => {
    // This callback will be used by WorkflowBuilder to refresh data after run completion
    queryClient.invalidateQueries({ queryKey: [`/api/workflow-runs?workflowId=${id}`] });
    setIsRunModalOpen(false);
  }, [id, queryClient]);

  // Handle toggle favorite
  const handleToggleFavorite = () => {
    if (workflow) {
      toggleFavoriteMutation.mutate(workflow.id);
    }
  };

  // Handle toggle history panel
  const handleToggleHistory = useCallback(() => {
    setIsHistoryOpen(prev => !prev);
  }, []);

  // Show loading state
  if (isLoading || createWorkflowMutation.isPending) {
    return (
      <div className="flex flex-col h-full">
        <div className="h-14 border-b border-neutral-200 dark:border-neutral-700 flex items-center px-4">
          <Loader2 className="w-5 h-5 animate-spin text-neutral-400" />
          <span className="ml-2 text-neutral-500">Loading workflow...</span>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-12 h-12 animate-spin text-primary mx-auto mb-4" />
            <p className="text-neutral-600 dark:text-neutral-300">Loading workflow editor...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !workflow) {
    return (
      <div className="flex flex-col h-full">
        <div className="h-14 border-b border-neutral-200 dark:border-neutral-700 flex items-center px-4">
          <span className="text-red-500">Error loading workflow</span>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md p-6 bg-white dark:bg-neutral-800 rounded-lg shadow-md">
            <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-2">Failed to load workflow</h2>
            <p className="text-neutral-600 dark:text-neutral-300 mb-4">{error.message}</p>
            <button
              onClick={() => setLocation('/workflows')}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
            >
              Back to Workflows
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render workflow editor
  return workflow ? (
    <div className="flex flex-col h-full">
      <TopNavigation
        workflowName={workflow.name}
        workflowStatus={workflow.status as 'draft' | 'published'}
        isFavorite={workflow.isFavorite}
        onRunWorkflow={handleRunWorkflowFromNav}
        onToggleFavorite={handleToggleFavorite}
        onToggleHistory={handleToggleHistory}
        isHistoryOpen={isHistoryOpen}
      />
      <div className="flex flex-1 overflow-hidden">
        <div className={`transition-all duration-300 ${isHistoryOpen ? 'flex-1' : 'w-full'}`}>
          <WorkflowBuilder
            workflow={workflow}
            onSave={handleSave}
            onRun={handleRunWorkflowComplete}
            layoutKey={isHistoryOpen ? 'history-open' : 'history-closed'}
          />
        </div>

        {isHistoryOpen && (
          <div className="w-96 border-l border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 flex-shrink-0">
            <WorkflowRunHistory
              workflow={workflow}
              onClose={handleToggleHistory}
              selectedRunId={selectedHistoryRunId}
              onRunSelect={setSelectedHistoryRunId}
            />
          </div>
        )}
      </div>

      {/* Run Workflow Modal */}
      {isRunModalOpen && workflow && (
        <RunWorkflowModal
          workflow={workflow}
          onClose={() => setIsRunModalOpen(false)}
          onRunComplete={handleRunWorkflowComplete}
        />
      )}
    </div>
  ) : null;
};

export default WorkflowEditor;
