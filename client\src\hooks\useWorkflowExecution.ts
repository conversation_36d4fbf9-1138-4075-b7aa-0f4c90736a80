import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { WorkflowExecutionState, NodeExecutionState, WorkflowRun, NodeRun } from '@/types/workflow';
import { useToast } from '@/hooks/use-toast';

interface UseWorkflowExecutionProps {
  workflowId: number;
  enabled?: boolean;
  selectedRunId?: number; // Allow tracking a specific run instead of latest
}

export const useWorkflowExecution = ({ workflowId, enabled = true, selectedRunId }: UseWorkflowExecutionProps) => {
  const { toast } = useToast();
  const [executionState, setExecutionState] = useState<WorkflowExecutionState>({
    status: 'idle',
    nodeStates: {}
  });
  const previousStatusRef = useRef<string>('idle');

  // Get the latest workflow run
  const { data: workflowRuns, isLoading: isRunsLoading } = useQuery<WorkflowRun[]>({
    queryKey: [`/api/workflow-runs?workflowId=${workflowId}`],
    enabled,
    refetchInterval: (data) => {
      // Refetch every 2 seconds if there's an active run
      if (!data || !Array.isArray(data) || data.length === 0) return false;
      const latestRun = data[0];
      return latestRun && ['pending', 'running'].includes(latestRun.status) ? 2000 : false;
    },
  });

  const latestRun = workflowRuns?.[0];

  // Use selected run if provided, otherwise use latest run
  const targetRun = selectedRunId
    ? workflowRuns?.find(run => run.id === selectedRunId)
    : latestRun;

  // Get node runs for the target workflow run
  const { data: nodeRuns, isLoading: isNodeRunsLoading } = useQuery<NodeRun[]>({
    queryKey: [`/api/workflow-runs/${targetRun?.id}/node-runs`],
    enabled: !!targetRun?.id && enabled,
    refetchInterval: (data) => {
      // Only refetch if we're tracking the latest run and there are running nodes
      if (selectedRunId || !data || !Array.isArray(data)) return false;
      const hasRunningNodes = data.some(nodeRun => ['pending', 'running'].includes(nodeRun.status));
      return hasRunningNodes ? 2000 : false;
    },
  });

  // Update execution state when data changes
  useEffect(() => {
    if (!targetRun) {
      setExecutionState({
        status: 'idle',
        nodeStates: {}
      });
      return;
    }

    const newNodeStates: Record<string, NodeExecutionState> = {};

    // Update node states from node runs
    if (nodeRuns && Array.isArray(nodeRuns)) {
      nodeRuns.forEach(nodeRun => {
        if (nodeRun && nodeRun.nodeId) {
          newNodeStates[nodeRun.nodeId] = {
            nodeId: nodeRun.nodeId,
            status: nodeRun.status as NodeExecutionState['status'],
            startTime: nodeRun.startTime,
            endTime: nodeRun.endTime || undefined,
            executionDuration: nodeRun.executionDuration || undefined,
            input: nodeRun.input,
            output: nodeRun.output,
            error: nodeRun.error || undefined,
            nodeRunId: nodeRun.id
          };
        }
      });
    }

    const newStatus = targetRun.status as WorkflowExecutionState['status'];

    // Show toast notification when execution completes (only for latest run, not selected historical runs)
    if (!selectedRunId && previousStatusRef.current === 'running' && (newStatus === 'completed' || newStatus === 'failed')) {
      const summary = Object.values(newNodeStates);
      const completedCount = summary.filter(s => s.status === 'completed').length;
      const failedCount = summary.filter(s => s.status === 'failed').length;

      toast({
        title: newStatus === 'completed' ? 'Workflow Completed' : 'Workflow Failed',
        description: newStatus === 'completed'
          ? `Successfully executed ${completedCount} nodes`
          : `${failedCount} nodes failed, ${completedCount} completed`,
        variant: newStatus === 'completed' ? 'default' : 'destructive'
      });
    }

    // Only update previous status ref for latest run tracking
    if (!selectedRunId) {
      previousStatusRef.current = newStatus;
    }

    setExecutionState({
      workflowRunId: targetRun.id,
      status: newStatus,
      nodeStates: newNodeStates,
      startTime: targetRun.startTime,
      endTime: targetRun.endTime || undefined
    });
  }, [targetRun, nodeRuns, toast, selectedRunId]);

  // Get execution state for a specific node
  const getNodeExecutionState = useCallback((nodeId: string): NodeExecutionState => {
    return executionState.nodeStates[nodeId] || {
      nodeId,
      status: 'idle'
    };
  }, [executionState.nodeStates]);

  // Check if workflow is currently executing
  const isExecuting = executionState.status === 'running' || executionState.status === 'pending';

  // Check if any nodes are currently executing
  const hasExecutingNodes = Object.values(executionState.nodeStates).some(
    nodeState => nodeState.status === 'running' || nodeState.status === 'pending'
  );

  // Get execution summary
  const getExecutionSummary = useCallback(() => {
    const nodeStates = Object.values(executionState.nodeStates);
    return {
      total: nodeStates.length,
      completed: nodeStates.filter(state => state.status === 'completed').length,
      failed: nodeStates.filter(state => state.status === 'failed').length,
      running: nodeStates.filter(state => state.status === 'running').length,
      pending: nodeStates.filter(state => state.status === 'pending').length,
      idle: nodeStates.filter(state => state.status === 'idle').length
    };
  }, [executionState.nodeStates]);

  // Reset execution state (useful when starting a new run)
  const resetExecutionState = useCallback(() => {
    setExecutionState({
      status: 'idle',
      nodeStates: {}
    });
  }, []);

  // Start tracking a new workflow run
  const startTracking = useCallback((workflowRunId: number) => {
    setExecutionState(prev => ({
      ...prev,
      workflowRunId,
      status: 'pending'
    }));
  }, []);

  return {
    executionState,
    getNodeExecutionState,
    isExecuting,
    hasExecutingNodes,
    getExecutionSummary,
    resetExecutionState,
    startTracking,
    isLoading: isRunsLoading || isNodeRunsLoading,
    latestRun,
    targetRun, // The run being tracked (selected or latest)
    nodeRuns,
    workflowRuns // All runs for this workflow
  };
};

export default useWorkflowExecution;
