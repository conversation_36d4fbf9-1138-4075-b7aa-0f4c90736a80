import React, { useState } from 'react';
import { <PERSON> } from 'wouter';
import { Star, Clock, MoreVertical, Pencil, Trash2 } from 'lucide-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Workflow } from '@/types/workflow';
import { formatDistanceToNow } from 'date-fns';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface WorkflowItemProps {
  workflow?: Workflow;
  id?: number;
  name?: string;
  lastEdited?: string;
  status?: 'draft' | 'published';
  isFavorite?: boolean;
  nodeCount?: number;
  isActive?: boolean;
  linkToEditor?: boolean;
}

const WorkflowItem: React.FC<WorkflowItemProps> = ({
  workflow,
  id: propId,
  name: propName,
  lastEdited: propLastEdited,
  status: propStatus,
  isFavorite: propIsFavorite,
  nodeCount: propNodeCount,
  isActive: propIsActive,
  linkToEditor = false
}) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(workflow?.name || propName || '');
  
  // Use either props or workflow object
  const id = workflow?.id || propId || 0;
  const name = workflow?.name || propName || '';
  const status = workflow?.status || propStatus || 'draft';
  const isFavorite = workflow?.isFavorite || propIsFavorite || false;
  const isActive = propIsActive || false;
  
  const lastEdited = workflow 
    ? formatDistanceToNow(new Date(workflow.updatedAt), { addSuffix: true })
    : propLastEdited || '';
  
  const nodeCount = workflow 
    ? Object.keys(workflow.nodes).length
    : propNodeCount || 0;
  
  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', `/api/workflows/${id}/favorite`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
    }
  });

  // Update workflow name mutation
  const updateWorkflowMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('PATCH', `/api/workflows/${id}`, {
        name: editedName
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
      setIsEditing(false);
      toast({
        title: "Workflow updated",
        description: "The workflow name has been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update workflow",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete workflow mutation
  const deleteWorkflowMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('DELETE', `/api/workflows/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
      toast({
        title: "Workflow deleted",
        description: "The workflow has been deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to delete workflow",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavoriteMutation.mutate();
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this workflow?')) {
      deleteWorkflowMutation.mutate();
    }
  };

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (editedName.trim()) {
      updateWorkflowMutation.mutate();
    }
  };

  const containerClasses = isActive
    ? 'p-3 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-primary rounded cursor-pointer'
    : 'p-3 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-primary dark:hover:border-primary rounded cursor-pointer';

  return (
    <Link href={linkToEditor ? `/workflows/editor/${id}` : `#`}>
      <div className={containerClasses}>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            {isEditing ? (
              <form onSubmit={handleNameSubmit} className="flex items-center gap-2">
                <Input
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                  className="h-8"
                  autoFocus
                />
                <Button type="submit" size="sm" variant="ghost">Save</Button>
                <Button 
                  type="button" 
                  size="sm" 
                  variant="ghost"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setIsEditing(false);
                    setEditedName(name);
                  }}
                >
                  Cancel
                </Button>
              </form>
            ) : (
              <h3 className="font-medium text-neutral-800 dark:text-white">{name}</h3>
            )}
            <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1 flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              Last edited: {lastEdited}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button 
              onClick={handleToggleFavorite}
              className={`text-${isFavorite ? 'yellow' : 'neutral'}-500 hover:text-yellow-500`}
            >
              <Star className={`w-4 h-4 ${isFavorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />
            </button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button 
                  className="text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="w-4 h-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Pencil className="w-4 h-4 mr-2" />
                  Edit Name
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={handleDelete}
                  className="text-red-600 dark:text-red-400"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="flex items-center mt-2 text-xs">
          <span className={`px-2 py-0.5 rounded-full ${
            status === 'published' 
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' 
              : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
          }`}>
            {status === 'published' ? 'Published' : 'Draft'}
          </span>
          <span className="ml-2 text-neutral-500 dark:text-neutral-400">{nodeCount} nodes</span>
        </div>
      </div>
    </Link>
  );
};

export default WorkflowItem;
