import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { <PERSON><PERSON><PERSON>cle, X<PERSON>ircle, Clock, Loader2, Circle } from 'lucide-react';

export interface BaseNodeProps extends Partial<NodeProps> {
  id: string;
  data: {
    name: string;
    description?: string;
  };
  icon: React.ReactNode;
  color: string;
  hasInput?: boolean;
  hasOutput?: boolean;
  children?: React.ReactNode;
  selected?: boolean;
  executionStatus?: 'idle' | 'pending' | 'running' | 'completed' | 'failed';
  onExecutionStatusClick?: () => void;
}

const BaseNode: React.FC<BaseNodeProps> = ({
  id,
  data,
  icon,
  color,
  hasInput = true,
  hasOutput = true,
  selected,
  children,
  dragging,
  type,
  xPos,
  yPos,
  zIndex,
  isConnectable = true,
  targetPosition,
  sourcePosition,
  executionStatus = 'idle',
  onExecutionStatusClick
}) => {
  // Create dynamic styles for the color since Tailwind dynamic classes don't work reliably
  const headerStyle = {
    backgroundColor: color
  };

  const borderStyle = selected ? {
    borderColor: color,
    borderWidth: '2px'
  } : {};

  // Get execution status indicator
  const getExecutionStatusIndicator = () => {
    const handleClick = (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent node selection
      if (onExecutionStatusClick) {
        onExecutionStatusClick();
      }
    };

    const baseClasses = "w-4 h-4 cursor-pointer hover:scale-110 transition-transform";

    switch (executionStatus) {
      case 'pending':
        return (
          <div title="Pending execution - Click to view details" onClick={handleClick}>
            <Clock className={`${baseClasses} text-yellow-500`} />
          </div>
        );
      case 'running':
        return (
          <div title="Currently running - Click to view details" onClick={handleClick}>
            <Loader2 className={`${baseClasses} text-blue-500 animate-spin`} />
          </div>
        );
      case 'completed':
        return (
          <div title="Completed successfully - Click to view details" onClick={handleClick}>
            <CheckCircle className={`${baseClasses} text-green-500`} />
          </div>
        );
      case 'failed':
        return (
          <div title="Execution failed - Click to view details" onClick={handleClick}>
            <XCircle className={`${baseClasses} text-red-500`} />
          </div>
        );
      case 'idle':
      default:
        // Show "not executed" indicator only if there's a click handler (meaning we have execution context)
        return onExecutionStatusClick ? (
          <div title="Not executed - Click to view details" onClick={handleClick}>
            <Circle className={`${baseClasses} text-gray-400`} />
          </div>
        ) : null;
    }
  };

  // Get execution status border color
  const getExecutionBorderStyle = () => {
    if (executionStatus === 'idle') return {};

    const statusColors = {
      pending: '#eab308', // yellow-500
      running: '#3b82f6', // blue-500
      completed: '#22c55e', // green-500
      failed: '#ef4444' // red-500
    };

    return {
      borderColor: statusColors[executionStatus],
      borderWidth: '2px',
      boxShadow: `0 0 0 1px ${statusColors[executionStatus]}40` // 25% opacity
    };
  };

  const handleStyle = {
    backgroundColor: color,
    width: '14px',
    height: '14px',
    border: '3px solid white',
    borderRadius: '50%',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    transition: 'all 0.2s ease'
  };

  // Handle mouse events to prevent node drag when interacting with handles
  const handleMouseDown = (event: React.MouseEvent) => {
    // Stop propagation to prevent node drag when clicking on handles
    event.stopPropagation();
    event.preventDefault();
  };

  // Prevent drag start on handles
  const handleDragStart = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  };

  // Handle link click to prevent interference with drag
  const handleLinkClick = (event: React.MouseEvent) => {
    event.stopPropagation();
  };

  return (
    <div className="relative">
      {/* Input Handle - positioned outside the main container */}
      {hasInput && (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          style={{
            ...handleStyle,
            left: '-6px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 10,
            cursor: 'crosshair'
          }}
          isConnectable={isConnectable}
          onMouseDown={handleMouseDown}
          onDragStart={handleDragStart}
        />
      )}

      {/* Output Handle - positioned outside the main container */}
      {hasOutput && (
        <Handle
          type="source"
          position={Position.Right}
          id="output"
          style={{
            ...handleStyle,
            right: '-6px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 10,
            cursor: 'crosshair'
          }}
          isConnectable={isConnectable}
          onMouseDown={handleMouseDown}
          onDragStart={handleDragStart}
        />
      )}

      {/* Main node container */}
      <div
        className={`relative bg-white dark:bg-neutral-800 rounded-lg shadow-md border-2 transition-all duration-200 ${
          selected ? 'ring-2 ring-primary' : ''
        }`}
        style={{
          ...borderStyle,
          ...getExecutionBorderStyle()
        }}
        onMouseDown={handleLinkClick}
      >
        <div
          className="px-3 py-2 text-white flex items-center justify-between"
          style={headerStyle}
        >
          <div className="flex items-center">
            {icon}
            <span className="font-medium ml-2">{data.name || 'Unnamed Node'}</span>
          </div>
          <div className="flex items-center space-x-1">
            {/* Execution status indicator */}
            {getExecutionStatusIndicator()}
            <button className="p-1 hover:bg-black/20 rounded">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="3" />
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
              </svg>
            </button>
            <button className="p-1 hover:bg-black/20 rounded">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="1" />
                <circle cx="19" cy="12" r="1" />
                <circle cx="5" cy="12" r="1" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-3">
          <div className="text-sm font-medium text-neutral-800 dark:text-white">{data.name || 'Unnamed Node'}</div>
          {data.description && (
            <div className="mt-1.5 text-xs text-neutral-500 dark:text-neutral-400">{data.description}</div>
          )}
          {/* Node-specific content rendered via children prop */}
          {children}
        </div>
      </div>
    </div>
  );
};

export default BaseNode;
