import React from 'react';
import { Link } from 'wouter';
import { History, Settings, PlayCircle, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface TopNavigationProps {
  workflowName: string;
  workflowStatus: 'draft' | 'published';
  isFavorite: boolean;
  onRunWorkflow: () => void;
  onToggleFavorite: () => void;
  onToggleHistory?: () => void;
  isHistoryOpen?: boolean;
}

const TopNavigation: React.FC<TopNavigationProps> = ({
  workflowName,
  workflowStatus,
  isFavorite,
  onRunWorkflow,
  onToggleFavorite,
  onToggleHistory,
  isHistoryOpen
}) => {
  const { toast } = useToast();

  const handleRunWorkflow = () => {
    onRunWorkflow();
  };

  return (
    <header className="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 h-14 flex items-center px-4 justify-between">
      <div className="flex items-center">
        <h2 className="font-semibold text-neutral-800 dark:text-white">{workflowName}</h2>
        <div className="ml-4 flex items-center space-x-2">
          <span className={`px-2 py-0.5 text-xs rounded-full ${
            workflowStatus === 'published'
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
          }`}>
            {workflowStatus === 'published' ? 'Published' : 'Draft'}
          </span>
          <button
            className={`text-${isFavorite ? 'yellow' : 'neutral'}-500 hover:text-yellow-500`}
            title={isFavorite ? "Remove from favorites" : "Add to favorites"}
            onClick={onToggleFavorite}
          >
            <Star className={`w-4 h-4 ${isFavorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />
          </button>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        {onToggleHistory ? (
          <Button
            variant={isHistoryOpen ? "default" : "outline"}
            size="sm"
            className="text-sm"
            onClick={onToggleHistory}
          >
            <History className="w-4 h-4 mr-1.5" />
            History
          </Button>
        ) : (
          <Link href="/run-history">
            <Button variant="outline" size="sm" className="text-sm">
              <History className="w-4 h-4 mr-1.5" />
              History
            </Button>
          </Link>
        )}
        <Link href="/settings">
          <Button variant="outline" size="sm" className="text-sm">
            <Settings className="w-4 h-4 mr-1.5" />
            Settings
          </Button>
        </Link>
        <Button onClick={handleRunWorkflow} className="text-sm bg-primary hover:bg-primary/90">
          <PlayCircle className="w-4 h-4 mr-1.5" />
          Run Workflow
        </Button>
      </div>
    </header>
  );
};

export default TopNavigation;
