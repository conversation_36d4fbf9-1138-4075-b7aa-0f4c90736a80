import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { WorkflowRun, Workflow } from '@/types/workflow';
import { CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Search,
  RefreshCw,
  X
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { format, formatDistanceToNow } from 'date-fns';
import RunStatusModal from '@/components/workflows/modals/RunStatusModal';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface WorkflowRunHistoryProps {
  workflow: Workflow;
  onClose?: () => void;
  selectedRunId?: number; // Allow external control of selected run
  onRunSelect?: (runId: number) => void; // Callback when run is selected
}

const WorkflowRunHistory: React.FC<WorkflowRunHistoryProps> = ({
  workflow,
  onClose,
  selectedRunId,
  onRunSelect
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedRun, setSelectedRun] = useState<{runId: number, workflowId: number} | null>(null);

  // Fetch workflow runs for this specific workflow
  const { data: workflowRuns, isLoading: isRunsLoading, refetch } = useQuery<WorkflowRun[]>({
    queryKey: [`/api/workflow-runs?workflowId=${workflow.id}`],
  });

  // Filter runs based on search term
  const filteredRuns = workflowRuns?.filter(run => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      run.status.toLowerCase().includes(searchLower) ||
      run.triggerType.toLowerCase().includes(searchLower) ||
      format(new Date(run.startTime), 'PPpp').toLowerCase().includes(searchLower)
    );
  }) || [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400',
      failed: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400',
      running: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400',
      pending: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'
    };

    return (
      <Badge className={variants[status as keyof typeof variants] || variants.pending}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(1)}s`;
  };

  // Handle run selection
  const handleRunSelect = (runId: number) => {
    if (onRunSelect) {
      onRunSelect(runId);
    } else {
      setSelectedRun({ runId, workflowId: workflow.id });
    }
  };

  // Get currently selected run for highlighting
  const currentSelectedRunId = selectedRunId || selectedRun?.runId;

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 pb-4">
        <div className="flex items-center justify-between mb-4">
          <CardTitle className="text-lg">Execution History</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isRunsLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-1.5 ${isRunsLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {onClose && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Quick Run Selector */}
        {filteredRuns.length > 0 && (
          <div className="mb-4">
            <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 block">
              Quick Select Run
            </label>
            <Select
              value={currentSelectedRunId?.toString() || ''}
              onValueChange={(value) => handleRunSelect(parseInt(value))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a run to view on canvas..." />
              </SelectTrigger>
              <SelectContent>
                {filteredRuns.map((run) => (
                  <SelectItem key={run.id} value={run.id.toString()}>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(run.status)}
                      <span className="font-medium">
                        Run #{run.id}
                      </span>
                      <span className="text-xs text-neutral-500">
                        {format(new Date(run.startTime), 'MMM d, HH:mm')}
                      </span>
                      <span className="text-xs capitalize">
                        ({run.status})
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {currentSelectedRunId && (
              <div className="mt-2 text-xs text-primary font-medium">
                📊 Showing execution data for Run #{currentSelectedRunId} on canvas
              </div>
            )}
          </div>
        )}

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
          <Input
            placeholder="Search executions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="flex-1 overflow-auto px-6">
        {isRunsLoading ? (
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-4 p-3 border border-neutral-200 dark:border-neutral-700 rounded-lg">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        ) : filteredRuns.length > 0 ? (
          <div className="space-y-2">
            {filteredRuns.map((run) => {
              const isSelected = currentSelectedRunId === run.id;
              return (
                <div
                  key={run.id}
                  className={`flex items-center justify-between p-3 border rounded-lg transition-all cursor-pointer ${
                    isSelected
                      ? 'border-primary bg-primary/5 dark:bg-primary/10 shadow-sm'
                      : 'border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800/50'
                  }`}
                  onClick={() => handleRunSelect(run.id)}
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(run.status)}
                        <span className="text-xs font-medium text-neutral-600 dark:text-neutral-400">
                          #{run.id}
                        </span>
                      </div>
                      {getStatusBadge(run.status)}
                    </div>

                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        {format(new Date(run.startTime), 'MMM d, yyyy HH:mm:ss')}
                      </span>
                      <span className="text-xs text-neutral-500">
                        {formatDistanceToNow(new Date(run.startTime), { addSuffix: true })}
                      </span>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-neutral-600 dark:text-neutral-400">
                      <span className="capitalize">{run.triggerType}</span>
                      <span>{formatDuration(run.executionDuration)}</span>
                      {run.totalNodes && (
                        <span>{run.completedNodes || 0}/{run.totalNodes} nodes</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {isSelected && (
                      <div className="text-xs text-primary font-medium flex items-center space-x-1">
                        <span>📊</span>
                        <span>On Canvas</span>
                      </div>
                    )}
                    <Button
                      variant={isSelected ? "default" : "ghost"}
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedRun({ runId: run.id, workflowId: workflow.id });
                      }}
                    >
                      <Eye className="w-4 h-4 mr-1.5" />
                      Details
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12 border border-dashed border-neutral-300 dark:border-neutral-700 rounded-lg">
            <Clock className="mx-auto h-12 w-12 text-neutral-400" />
            <p className="mt-4 text-neutral-600 dark:text-neutral-400">
              {searchTerm ? 'No matching executions found' : 'No executions yet'}
            </p>
            <p className="text-sm text-neutral-500 dark:text-neutral-500">
              {searchTerm ? 'Try adjusting your search terms' : 'Run this workflow to see execution history'}
            </p>
          </div>
        )}
      </div>

      {selectedRun && (
        <RunStatusModal
          workflow={workflow}
          runId={selectedRun.runId}
          onClose={() => setSelectedRun(null)}
        />
      )}
    </div>
  );
};

export default WorkflowRunHistory;
