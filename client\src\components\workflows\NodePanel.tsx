import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Code,
  Globe,
  MessageSquare,
  Bot,
  GitBranch,
  Settings,
  Puzzle
} from 'lucide-react';

const NodePanel: React.FC = () => {
  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('application/node-panel', 'true'); // Mark as node panel drag
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <div className="w-80 bg-white dark:bg-neutral-800 border-l border-neutral-200 dark:border-neutral-700 flex flex-col">
      <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
        <h3 className="font-medium text-neutral-800 dark:text-white">Nodes</h3>
        <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">Drag nodes onto the canvas</p>
      </div>

      <div className="p-4 overflow-y-auto scrollbar-thin flex-1">
        <div className="space-y-3">
          <div className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase">Triggers</div>

          {/* Input Node */}
          <div
            className="flex items-center p-2 bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded cursor-grab hover:border-[#0078D4] dark:hover:border-[#0078D4]"
            draggable
            onDragStart={(event) => onDragStart(event, 'input')}
          >
            <div className="w-8 h-8 bg-[#0078D4] rounded flex items-center justify-center text-white">
              <ArrowRight size={16} />
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-neutral-800 dark:text-white">Input</div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400">Manual workflow input</div>
            </div>
          </div>

          {/* API Trigger Node */}
          <div
            className="flex items-center p-2 bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded cursor-grab hover:border-[#5C2D91] dark:hover:border-[#5C2D91]"
            draggable
            onDragStart={(event) => onDragStart(event, 'api-trigger')}
          >
            <div className="w-8 h-8 bg-[#5C2D91] rounded flex items-center justify-center text-white">
              <Code size={16} />
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-neutral-800 dark:text-white">API Trigger</div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400">Webhook trigger</div>
            </div>
          </div>

          <div className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase mt-6">AI Nodes</div>

          {/* Prompt Node */}
          <div
            className="flex items-center p-2 bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded cursor-grab hover:border-[#FF8C00] dark:hover:border-[#FF8C00]"
            draggable
            onDragStart={(event) => onDragStart(event, 'prompt')}
          >
            <div className="w-8 h-8 bg-[#FF8C00] rounded flex items-center justify-center text-white">
              <MessageSquare size={16} />
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-neutral-800 dark:text-white">Prompt</div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400">LLM prompt template</div>
            </div>
          </div>

          {/* Agent Node */}
          <div
            className="flex items-center p-2 bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded cursor-grab hover:border-[#107C10] dark:hover:border-[#107C10]"
            draggable
            onDragStart={(event) => onDragStart(event, 'agent')}
          >
            <div className="w-8 h-8 bg-[#107C10] rounded flex items-center justify-center text-white">
              <Bot size={16} />
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-neutral-800 dark:text-white">Agent</div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400">LLM with system role</div>
            </div>
          </div>

          <div className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase mt-6">Actions</div>

          {/* API Node */}
          <div
            className="flex items-center p-2 bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded cursor-grab hover:border-[#0078D4] dark:hover:border-[#0078D4]"
            draggable
            onDragStart={(event) => onDragStart(event, 'api')}
          >
            <div className="w-8 h-8 bg-[#0078D4] rounded flex items-center justify-center text-white">
              <Globe size={16} />
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-neutral-800 dark:text-white">API Call</div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400">HTTP request to API</div>
            </div>
          </div>

          <div className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase mt-6">Custom</div>

          {/* Custom Node */}
          <div
            className="flex items-center p-2 bg-neutral-50 dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded cursor-grab hover:border-[#D83B01] dark:hover:border-[#D83B01]"
            draggable
            onDragStart={(event) => onDragStart(event, 'custom')}
          >
            <div className="w-8 h-8 bg-[#D83B01] rounded flex items-center justify-center text-white">
              <Puzzle size={16} />
            </div>
            <div className="ml-3">
              <div className="text-sm font-medium text-neutral-800 dark:text-white">Custom Node</div>
              <div className="text-xs text-neutral-500 dark:text-neutral-400">Create your own node</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NodePanel;
