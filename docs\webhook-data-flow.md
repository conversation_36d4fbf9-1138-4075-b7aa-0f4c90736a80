# Enhanced Webhook Data Flow and Validation

This document explains the improved webhook trigger system and how data flows between API Trigger nodes, Prompt nodes, and Agent nodes with proper validation.

## Overview

The enhanced system provides:
1. **Robust webhook data processing** in API Trigger nodes
2. **Advanced template variable replacement** in Prompt nodes
3. **Intelligent input processing** in Agent nodes
4. **Comprehensive input validation** across all node types

## API Trigger Node Enhancements

### Data Structure
API Trigger nodes now pass a structured data object to downstream nodes:

```json
{
  "method": "POST",
  "headers": {
    "content-type": "application/json",
    "user-agent": "GitHub-Hookshot/abc123"
  },
  "query": {
    "action": "opened"
  },
  "body": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "message": "Hello world"
  },
  "params": {
    "id": "123"
  },
  "timestamp": "2025-01-27T10:30:00Z",
  "triggerNodeId": 456,
  "validation": {
    "enabled": true,
    "validatedData": { /* validated portion */ },
    "validationTarget": "request_body"
  }
}
```

### HTTP Method-Specific Validation
- **GET**: Validates query parameters
- **POST/PUT/PATCH**: Validates request body (falls back to query params)
- **DELETE**: Validates request body or query parameters
- **Other methods**: Validates entire request structure

## Prompt Node Enhancements

### Advanced Template Variables

#### Basic Variable Access
```
{{method}}           → "POST"
{{body.name}}        → "John Doe"
{{query.action}}     → "opened"
{{headers.user-agent}} → "GitHub-Hookshot/abc123"
```

#### Webhook Helper Functions
```
{{webhook.method}}      → HTTP method
{{webhook.body_json}}   → Pretty-printed JSON of request body
{{webhook.query_params}} → Pretty-printed JSON of query parameters
{{webhook.headers_json}} → Pretty-printed JSON of headers
{{webhook.all_data}}    → Pretty-printed JSON of entire webhook data
```

#### JSON Helper Functions
```
{{json.body}}        → JSON formatted request body
{{json.query}}       → JSON formatted query parameters
{{json.headers}}     → JSON formatted headers
{{json.params}}      → JSON formatted URL parameters
```

### Example Prompt Template
```
You received a {{webhook.method}} request from {{headers.user-agent}}.

Request details:
- User: {{body.name}} ({{body.email}})
- Message: {{body.message}}
- Action: {{query.action}}

Full request body:
{{webhook.body_json}}

Please process this webhook and provide an appropriate response.
```

### Input Validation
Prompt nodes can now validate incoming data:

```json
{
  "inputValidation": {
    "enabled": true,
    "schema": {
      "type": "object",
      "properties": {
        "body": {
          "type": "object",
          "properties": {
            "name": { "type": "string", "minLength": 1 },
            "email": { "type": "string", "format": "email" }
          },
          "required": ["name", "email"]
        }
      },
      "required": ["body"]
    }
  }
}
```

## Agent Node Enhancements

### Intelligent Input Processing
Agent nodes automatically detect webhook data and create structured prompts:

#### For Webhook Data:
```
I received a webhook request with the following details:

HTTP Method: POST

Request Body:
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "Hello world"
}

Query Parameters:
{
  "action": "opened"
}

Relevant Headers:
{
  "content-type": "application/json",
  "user-agent": "GitHub-Hookshot/abc123"
}

Validation: request_body was validated successfully

Please process this webhook data according to your system prompt and provide an appropriate response.
```

#### For Generic Data:
```
I received the following input data:
{
  "key": "value",
  "data": { ... }
}

Please process this input data according to your system prompt and provide an appropriate response.
```

### Custom Prompt Templates
Agent nodes support custom prompt templates for specialized processing:

```json
{
  "promptTemplate": "Analyze this GitHub webhook and determine if it requires immediate attention. Focus on security implications and breaking changes."
}
```

### Input Validation
Similar to Prompt nodes, Agent nodes can validate incoming data:

```json
{
  "inputValidation": {
    "enabled": true,
    "schema": {
      "type": "object",
      "properties": {
        "method": { "type": "string" },
        "body": { "type": "object" }
      },
      "required": ["method"]
    }
  }
}
```

## Common Validation Schemas

### GitHub Webhook
```json
{
  "type": "object",
  "properties": {
    "action": { "type": "string" },
    "repository": {
      "type": "object",
      "properties": {
        "name": { "type": "string" },
        "full_name": { "type": "string" }
      }
    }
  },
  "required": ["action", "repository"]
}
```

### Form Submission
```json
{
  "type": "object",
  "properties": {
    "name": { "type": "string", "minLength": 1 },
    "email": { "type": "string", "format": "email" },
    "message": { "type": "string", "minLength": 1 }
  },
  "required": ["name", "email"]
}
```

## Error Handling

### Missing Template Variables
When template variables are missing, the system:
1. Logs a warning with available keys
2. Replaces missing variables with `[variable_name: not found]`
3. Continues execution (non-blocking)

### Validation Failures
When input validation fails:
1. Logs detailed error information
2. Throws an error with specific validation messages
3. Stops workflow execution

### Example Error Messages
```
Prompt node input validation failed: instance.body.email is not of a type(s) string, instance.body.name is required
```

## Best Practices

### 1. Use Specific Template Variables
```
Good: {{body.user.name}}
Avoid: {{user_name}} (if data is nested)
```

### 2. Provide Fallbacks
```
User: {{body.name}} ({{body.email}})
Fallback: User: {{body.name || 'Unknown'}} ({{body.email || 'No email'}})
```

### 3. Validate Critical Data
Always enable input validation for nodes that expect specific data structures.

### 4. Use Helper Functions
```
{{webhook.body_json}} provides better formatting than {{body}}
```

### 5. Log Template Variables
Enable debug logging to see which variables are available and which are missing.

## Testing

### Test Webhook Data Flow
1. Create an API Trigger node with validation enabled
2. Add a Prompt node with template variables
3. Add an Agent node for processing
4. Send test webhook requests with various data structures
5. Check logs for validation and processing details

### Example Test Requests
```bash
# Test POST with body
curl -X POST "http://localhost:3000/api/trigger/1/node-123" \
  -H "Content-Type: application/json" \
  -d '{"name": "John", "email": "<EMAIL>", "message": "Test"}'

# Test GET with query params
curl -X GET "http://localhost:3000/api/trigger/1/node-123?action=test&id=123"
```
