// Node Types
export type NodeType = 'input' | 'api' | 'api-trigger' | 'prompt' | 'agent' | 'custom';

export interface Position {
  x: number;
  y: number;
}

// Base Node Definition
export interface BaseNodeData {
  id: string;
  type: NodeType;
  position: Position;
  data: {
    name: string;
    description?: string;
  };
}

// Enhanced Input Schema Types
export type InputFieldType =
  | 'string'
  | 'number'
  | 'boolean'
  | 'date'
  | 'email'
  | 'url'
  | 'textarea'
  | 'select'
  | 'multiselect'
  | 'file'
  | 'array'
  | 'object';

export interface InputFieldValidation {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  customMessage?: string;
}

export interface InputFieldOption {
  label: string;
  value: string | number;
}

export interface InputField {
  id: string;
  name: string;
  label: string;
  type: InputFieldType;
  description?: string;
  placeholder?: string;
  defaultValue?: any;
  validation?: InputFieldValidation;
  options?: InputFieldOption[]; // For select/multiselect
  fields?: InputField[]; // For object/array types
}

export interface InputSchema {
  version: string;
  fields: InputField[];
  title?: string;
  description?: string;
}

// Input Node
export interface InputNodeData extends BaseNodeData {
  type: 'input';
  data: {
    name: string;
    description?: string;
    schema: InputSchema;
    // Legacy support for old simple schema format
    legacySchema?: Record<string, string>;
  };
}

// API Node (for making HTTP requests)
export interface ApiNodeData extends BaseNodeData {
  type: 'api';
  data: {
    name: string;
    description?: string;
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    authType?: 'none' | 'apiKey' | 'bearer';
    apiKeyHeader?: string;
    credentialId?: number | null;
    // New configuration options
    rateLimit?: {
      enabled: boolean;
      requestsPerMinute: number;
    };
    timeout?: {
      enabled: boolean;
      milliseconds: number;
    };
    requestValidation?: {
      enabled: boolean;
      schema: Record<string, any>;
    };
    responseFormat?: {
      type: 'json' | 'text' | 'binary';
      schema?: Record<string, any>;
    };
  };
}

// API Trigger Node (for receiving webhook requests)
export interface ApiTriggerNodeData extends BaseNodeData {
  type: 'api-trigger';
  data: {
    name: string;
    description?: string;
    allowedMethods: ('GET' | 'POST' | 'PUT' | 'DELETE')[];
    authType?: 'none' | 'apiKey' | 'bearer';
    apiKeyHeader?: string;
    credentialId?: number | null;
    // Webhook-specific configuration
    rateLimit?: {
      enabled: boolean;
      requestsPerMinute: number;
    };
    requestValidation?: {
      enabled: boolean;
      schema: Record<string, any>;
    };
    responseConfig?: {
      successStatus: number;
      successMessage: string;
      errorStatus: number;
      errorMessage: string;
    };
  };
}

// Prompt Node
export interface PromptNodeData extends BaseNodeData {
  type: 'prompt';
  data: {
    name: string;
    description?: string;
    prompt: string;
    model: string;
    provider: string;
    credentialId: number;
    maxTokens: number;
    temperature: number;
    outputFormat: 'text' | 'json' | 'markdown';
    schema?: Record<string, any>;
    // Input validation for incoming data
    inputValidation?: {
      enabled: boolean;
      schema: Record<string, any>;
    };
  };
}

// Agent Node
export interface AgentNodeData extends BaseNodeData {
  type: 'agent';
  data: {
    name: string;
    description?: string;
    systemPrompt: string;
    model: string;
    provider: string;
    credentialId: number;
    maxTokens: number;
    temperature: number;
    outputFormat: 'text' | 'json';
    schema?: Record<string, any>;
    // Input validation for incoming data
    inputValidation?: {
      enabled: boolean;
      schema: Record<string, any>;
    };
    // Custom prompt template for processing input data
    promptTemplate?: string;
  };
}

// Custom Node
export interface CustomNodeData extends BaseNodeData {
  type: 'custom';
  data: {
    name: string;
    description?: string;
    code: string;
    inputs: { name: string; type: string; required: boolean }[];
    outputs: { name: string; type: string }[];
  };
}

// Union type for all node types
export type NodeData =
  | InputNodeData
  | ApiNodeData
  | ApiTriggerNodeData
  | PromptNodeData
  | AgentNodeData
  | CustomNodeData;

// Edge definition
export interface Edge {
  id: string;
  source: string;
  target: string;
}

// Workflow definition
export interface Workflow {
  id: number;
  name: string;
  description?: string;
  nodes: Record<string, NodeData>;
  edges: Record<string, Edge>;
  status: 'draft' | 'published';
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Workflow Run
export interface WorkflowRun {
  id: number;
  workflowId: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime: Date | null;
  triggerType: 'manual' | 'api';
  input: any;
  logs: Record<string, any>;
  executionDuration?: number;
  totalNodes?: number;
  completedNodes?: number;
  failedNodes?: number;
  errorMessage?: string;
  stackTrace?: string;
}

// Node Run
export interface NodeRun {
  id: number;
  workflowRunId: number;
  nodeId: string;
  nodeType: string;
  nodeName?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime: Date | null;
  executionDuration?: number;
  input: any;
  output: any;
  error: string | null;
  stackTrace?: string;
  retryCount?: number;
  memoryUsage?: number;
  cpuTime?: number;
}

// Log Entry
export interface LogEntry {
  id: number;
  workflowRunId: number;
  nodeRunId?: number;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  details: Record<string, any>;
  timestamp: Date;
  source: 'workflow' | 'node' | 'system';
  category?: string;
}

// Node execution state for visual indicators
export interface NodeExecutionState {
  nodeId: string;
  status: 'idle' | 'pending' | 'running' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  executionDuration?: number;
  input?: any;
  output?: any;
  error?: string;
  nodeRunId?: number;
}

// Workflow execution state
export interface WorkflowExecutionState {
  workflowRunId?: number;
  status: 'idle' | 'pending' | 'running' | 'completed' | 'failed';
  nodeStates: Record<string, NodeExecutionState>;
  startTime?: Date;
  endTime?: Date;
}

// AI Credentials
export interface Credential {
  id: number;
  name: string;
  provider: string;
  apiKey: string;
  createdAt: Date;
}
