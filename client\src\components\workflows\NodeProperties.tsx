import React from 'react';
import { Node } from 'reactflow';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useQuery } from '@tanstack/react-query';
import { Credential, InputSchema } from '@/types/workflow';
import { X, Settings, ArrowRight, Code, MessageSquare, Bot, Puzzle } from 'lucide-react';

interface NodePropertiesProps {
  node: Node;
  onUpdate: (nodeId: string, data: any) => void;
  onClose: () => void;
  onConfigure: () => void;
}

const NodeProperties: React.FC<NodePropertiesProps> = ({ node, onUpdate, onClose, onConfigure }) => {
  const { data: credentials } = useQuery<Credential[]>({
    queryKey: ['/api/credentials'],
  });

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate(node.id, { ...node.data, name: e.target.value });
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdate(node.id, { ...node.data, description: e.target.value });
  };

  const getNodeIcon = () => {
    switch (node.type) {
      case 'input':
        return <ArrowRight className="text-[#0078D4]" />;
      case 'api':
        return <Code className="text-[#5C2D91]" />;
      case 'prompt':
        return <MessageSquare className="text-[#FF8C00]" />;
      case 'agent':
        return <Bot className="text-[#107C10]" />;
      case 'custom':
        return <Puzzle className="text-[#D83B01]" />;
      default:
        return <Settings />;
    }
  };

  return (
    <div className="w-80 bg-white dark:bg-neutral-800 border-l border-neutral-200 dark:border-neutral-700 flex flex-col">
      <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 flex justify-between items-center">
        <h3 className="font-medium text-neutral-800 dark:text-white flex items-center">
          {getNodeIcon()}
          <span className="ml-2">{node.data.name || `${node.type.charAt(0).toUpperCase() + node.type.slice(1)} Node`}</span>
        </h3>
        <button
          onClick={onClose}
          className="text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200"
        >
          <X size={18} />
        </button>
      </div>

      <div className="p-4 overflow-y-auto scrollbar-thin flex-1">
        <div className="space-y-4">
          <div>
            <Label htmlFor="node-name">Node Name</Label>
            <Input
              id="node-name"
              value={node.data.name || ''}
              onChange={handleNameChange}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="node-description">Description (optional)</Label>
            <Textarea
              id="node-description"
              value={node.data.description || ''}
              onChange={handleDescriptionChange}
              className="mt-1"
              rows={2}
            />
          </div>

          {node.type === 'input' && (
            <div>
              <Label>Input Schema</Label>
              <div className="mt-1 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs">
                {(() => {
                  const schema = node.data.schema;
                  const isNewSchema = schema && typeof schema === 'object' && 'fields' in schema;

                  if (isNewSchema) {
                    const inputSchema = schema as InputSchema;
                    return (
                      <div className="space-y-2">
                        <div className="font-medium">{inputSchema.title || 'Input Form'}</div>
                        {inputSchema.fields.length > 0 ? (
                          <div className="space-y-1">
                            {inputSchema.fields.map((field) => (
                              <div key={field.id} className="flex items-center justify-between">
                                <span className="text-neutral-600 dark:text-neutral-400">{field.label}</span>
                                <span className="text-neutral-500 text-xs">{field.type}</span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-neutral-500">No fields defined</div>
                        )}
                      </div>
                    );
                  } else {
                    // Legacy schema format
                    return (
                      <div className="font-mono">
                        {JSON.stringify(schema || { "query": "string" }, null, 2)}
                      </div>
                    );
                  }
                })()}
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                Configure the schema in the advanced settings
              </p>
            </div>
          )}

          {node.type === 'prompt' && (
            <>
              <div>
                <Label>LLM Model</Label>
                <Select
                  value={node.data.model || ''}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, model: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gemini 2.5 Pro Preview">Gemini 2.5 Pro Preview</SelectItem>
                    <SelectItem value="Gemini 2.0 Flash">Gemini 2.0 Flash</SelectItem>
                    <SelectItem value="Gemini 2.0 Flash Lite">Gemini 2.0 Flash Lite</SelectItem>
                    <SelectItem value="Gemini 1.5 Pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="Gemini Pro">Gemini Pro (Legacy)</SelectItem>
                    <SelectItem value="Gemini Flash">Gemini Flash (Legacy)</SelectItem>
                    <SelectItem value="DeepSeek Chat V3">DeepSeek Chat V3</SelectItem>
                    <SelectItem value="DeepSeek R1">DeepSeek R1</SelectItem>
                    <SelectItem value="DeepSeek R1T Chimera">DeepSeek R1T Chimera</SelectItem>
                    <SelectItem value="Qwen3 235B">Qwen3 235B</SelectItem>
                    <SelectItem value="Llama 4 Maverick">Llama 4 Maverick</SelectItem>
                    <SelectItem value="MAI DS R1">MAI DS R1</SelectItem>
                    <SelectItem value="Qwen 2.5 72B">Qwen 2.5 72B</SelectItem>
                    <SelectItem value="Qwen 2.5 32B">Qwen 2.5 32B</SelectItem>
                    <SelectItem value="Qwen 2.5 14B">Qwen 2.5 14B</SelectItem>
                    <SelectItem value="Gemma 2 9B">Gemma 2 9B</SelectItem>
                    <SelectItem value="Gemma 2 27B">Gemma 2 27B</SelectItem>
                    <SelectItem value="DeepSeek R1 Distill">DeepSeek R1 Distill</SelectItem>
                    <SelectItem value="DeepSeek Chat">DeepSeek Chat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Prompt Template</Label>
                <Textarea
                  value={node.data.prompt || ''}
                  onChange={(e) => onUpdate(node.id, { ...node.data, prompt: e.target.value })}
                  className="mt-1 font-mono"
                  rows={3}
                />
              </div>
            </>
          )}

          {node.type === 'agent' && (
            <>
              <div>
                <Label>LLM Model</Label>
                <Select
                  value={node.data.model || ''}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, model: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gemini 2.5 Pro Preview">Gemini 2.5 Pro Preview</SelectItem>
                    <SelectItem value="Gemini 2.0 Flash">Gemini 2.0 Flash</SelectItem>
                    <SelectItem value="Gemini 2.0 Flash Lite">Gemini 2.0 Flash Lite</SelectItem>
                    <SelectItem value="Gemini 1.5 Pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="Gemini Pro">Gemini Pro (Legacy)</SelectItem>
                    <SelectItem value="Gemini Flash">Gemini Flash (Legacy)</SelectItem>
                    <SelectItem value="DeepSeek Chat V3">DeepSeek Chat V3</SelectItem>
                    <SelectItem value="DeepSeek R1">DeepSeek R1</SelectItem>
                    <SelectItem value="DeepSeek R1T Chimera">DeepSeek R1T Chimera</SelectItem>
                    <SelectItem value="Qwen3 235B">Qwen3 235B</SelectItem>
                    <SelectItem value="Llama 4 Maverick">Llama 4 Maverick</SelectItem>
                    <SelectItem value="MAI DS R1">MAI DS R1</SelectItem>
                    <SelectItem value="Qwen 2.5 72B">Qwen 2.5 72B</SelectItem>
                    <SelectItem value="Qwen 2.5 32B">Qwen 2.5 32B</SelectItem>
                    <SelectItem value="Qwen 2.5 14B">Qwen 2.5 14B</SelectItem>
                    <SelectItem value="Gemma 2 9B">Gemma 2 9B</SelectItem>
                    <SelectItem value="Gemma 2 27B">Gemma 2 27B</SelectItem>
                    <SelectItem value="DeepSeek R1 Distill">DeepSeek R1 Distill</SelectItem>
                    <SelectItem value="DeepSeek Chat">DeepSeek Chat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>System Prompt</Label>
                <Textarea
                  value={node.data.systemPrompt || ''}
                  onChange={(e) => onUpdate(node.id, { ...node.data, systemPrompt: e.target.value })}
                  className="mt-1 font-mono"
                  rows={3}
                />
              </div>

              <div>
                <Label>Output Format</Label>
                <RadioGroup
                  value={node.data.outputFormat || 'text'}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, outputFormat: value })}
                  className="flex space-x-4 mt-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="text" id="text" />
                    <Label htmlFor="text">Text</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="json" id="json" />
                    <Label htmlFor="json">JSON</Label>
                  </div>
                  {(node.type === 'prompt' || node.type === 'agent') && (
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="markdown" id="markdown" />
                      <Label htmlFor="markdown">Markdown</Label>
                    </div>
                  )}
                </RadioGroup>
              </div>
            </>
          )}

          {node.type === 'api' && (
            <>
              <div>
                <Label>API Endpoint</Label>
                <Input
                  value={node.data.url || ''}
                  onChange={(e) => onUpdate(node.id, { ...node.data, url: e.target.value })}
                  className="mt-1"
                  placeholder="https://api.example.com/endpoint"
                />
              </div>

              <div>
                <Label>HTTP Method</Label>
                <Select
                  value={node.data.method || 'POST'}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, method: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="DELETE">DELETE</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}

          {node.type === 'api-trigger' && (
            <>
              <div>
                <Label>Allowed Methods</Label>
                <div className="mt-1 text-sm text-neutral-600 dark:text-neutral-400">
                  {node.data.allowedMethods?.join(', ') || 'All methods allowed'}
                </div>
              </div>

              <div>
                <Label>Authentication</Label>
                <div className="mt-1 text-sm text-neutral-600 dark:text-neutral-400">
                  {node.data.authType === 'none' ? 'Public endpoint' :
                   node.data.authType === 'apiKey' ? 'API Key required' :
                   'Bearer token required'}
                </div>
              </div>

              {node.data.requestValidation?.enabled && (
                <div>
                  <Label>Request Validation</Label>
                  <div className="mt-1 text-sm text-green-600 dark:text-green-400">
                    ✓ Enabled
                  </div>
                </div>
              )}

              {node.data.rateLimit?.enabled && (
                <div>
                  <Label>Rate Limiting</Label>
                  <div className="mt-1 text-sm text-amber-600 dark:text-amber-400">
                    {node.data.rateLimit.requestsPerMinute} requests/min
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      <div className="p-4 border-t border-neutral-200 dark:border-neutral-700">
        <Button onClick={onConfigure} className="w-full">
          <Settings size={16} className="mr-2" />
          Advanced Configuration
        </Button>
      </div>
    </div>
  );
};

export default NodeProperties;
