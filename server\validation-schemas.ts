// Common validation schemas for node inputs

export const webhookDataSchema = {
  type: "object",
  properties: {
    method: { type: "string" },
    headers: { type: "object" },
    query: { type: "object" },
    body: { type: "object" },
    params: { type: "object" },
    timestamp: { type: "string" },
    triggerNodeId: { type: "number" },
    validation: {
      type: "object",
      properties: {
        enabled: { type: "boolean" },
        validatedData: { type: "object" },
        validationTarget: { type: "string" }
      }
    }
  },
  required: ["method"]
};

export const promptNodeInputSchema = {
  type: "object",
  properties: {
    // Allow any properties for flexible input
  },
  additionalProperties: true
};

export const agentNodeInputSchema = {
  type: "object",
  properties: {
    // Allow any properties for flexible input
  },
  additionalProperties: true
};

// Common validation schemas for webhook request bodies
export const commonWebhookSchemas = {
  // GitHub webhook payload
  github: {
    type: "object",
    properties: {
      action: { type: "string" },
      repository: {
        type: "object",
        properties: {
          name: { type: "string" },
          full_name: { type: "string" },
          owner: {
            type: "object",
            properties: {
              login: { type: "string" }
            }
          }
        }
      },
      sender: {
        type: "object",
        properties: {
          login: { type: "string" }
        }
      }
    },
    required: ["action", "repository"]
  },

  // Slack webhook payload
  slack: {
    type: "object",
    properties: {
      token: { type: "string" },
      team_id: { type: "string" },
      channel_id: { type: "string" },
      user_id: { type: "string" },
      text: { type: "string" },
      trigger_word: { type: "string" }
    },
    required: ["token", "team_id", "channel_id", "user_id"]
  },

  // Discord webhook payload
  discord: {
    type: "object",
    properties: {
      content: { type: "string" },
      username: { type: "string" },
      avatar_url: { type: "string" },
      embeds: {
        type: "array",
        items: {
          type: "object",
          properties: {
            title: { type: "string" },
            description: { type: "string" },
            color: { type: "number" }
          }
        }
      }
    }
  },

  // Generic form submission
  form: {
    type: "object",
    properties: {
      name: { type: "string", minLength: 1 },
      email: { type: "string", format: "email" },
      message: { type: "string", minLength: 1 }
    },
    required: ["name", "email"]
  },

  // API data submission
  api: {
    type: "object",
    properties: {
      id: { type: ["string", "number"] },
      data: { type: "object" },
      timestamp: { type: "string" }
    },
    required: ["data"]
  }
};

// Template variable validation helpers
export const templateVariableHelpers = {
  // Webhook-specific helpers
  webhook: [
    'webhook.method',
    'webhook.body_json',
    'webhook.query_params',
    'webhook.headers_json',
    'webhook.all_data'
  ],

  // JSON formatting helpers
  json: [
    'json.body',
    'json.query',
    'json.headers',
    'json.params'
  ],

  // Common webhook data paths
  common: [
    'method',
    'body',
    'query',
    'headers',
    'params',
    'body.name',
    'body.email',
    'body.message',
    'query.id',
    'query.action',
    'headers.content-type',
    'headers.user-agent'
  ]
};

// Validation utility functions
export class ValidationUtils {
  static validateTemplateVariables(prompt: string, availableData: any): string[] {
    const missingVariables: string[] = [];
    const templateRegex = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = templateRegex.exec(prompt)) !== null) {
      const variable = match[1].trim();
      
      // Skip helper functions
      if (variable.startsWith('webhook.') || variable.startsWith('json.')) {
        continue;
      }

      // Check if variable exists in data
      if (!this.hasNestedProperty(availableData, variable)) {
        missingVariables.push(variable);
      }
    }

    return missingVariables;
  }

  static hasNestedProperty(obj: any, path: string): boolean {
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' && key in current ? current[key] : undefined;
    }, obj) !== undefined;
  }

  static extractTemplateVariables(prompt: string): string[] {
    const variables: string[] = [];
    const templateRegex = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = templateRegex.exec(prompt)) !== null) {
      const variable = match[1].trim();
      if (!variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }

  static generateInputValidationSchema(templateVariables: string[]): any {
    const properties: any = {};
    const required: string[] = [];

    for (const variable of templateVariables) {
      // Skip helper functions
      if (variable.startsWith('webhook.') || variable.startsWith('json.')) {
        continue;
      }

      const parts = variable.split('.');
      let current = properties;

      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        
        if (i === parts.length - 1) {
          // Last part - set the property type
          current[part] = { type: "string" };
          if (i === 0) {
            required.push(part);
          }
        } else {
          // Intermediate part - create nested object
          if (!current[part]) {
            current[part] = {
              type: "object",
              properties: {}
            };
          }
          current = current[part].properties;
        }
      }
    }

    return {
      type: "object",
      properties,
      required
    };
  }
}

// Pre-defined validation schemas for common use cases
export const preDefinedSchemas = {
  webhookData: webhookDataSchema,
  promptInput: promptNodeInputSchema,
  agentInput: agentNodeInputSchema,
  ...commonWebhookSchemas
};
