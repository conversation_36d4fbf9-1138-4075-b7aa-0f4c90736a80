import React, { useState, useEffect } from 'react';
import { Node } from 'reactflow';
import { useQuery } from '@tanstack/react-query';
import { NodeRun, LogEntry, NodeExecutionState } from '@/types/workflow';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  X,
  ArrowRight,
  Code,
  MessageSquare,
  Bot,
  Puzzle,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  Settings,
  Play,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { format } from 'date-fns';

interface NodeExecutionViewProps {
  node: Node;
  executionState?: NodeExecutionState;
  workflowRunId?: number;
  onClose: () => void;
  onConfigure: () => void;
  // Navigation props
  allNodes?: Node[]; // All nodes in the workflow for navigation
  onNavigateToNode?: (nodeId: string) => void; // Callback to navigate to a different node
}

const NodeExecutionView: React.FC<NodeExecutionViewProps> = ({
  node,
  executionState,
  workflowRunId,
  onClose,
  onConfigure,
  allNodes = [],
  onNavigateToNode
}) => {
  const [activeTab, setActiveTab] = useState('input');

  // Navigation logic
  const currentNodeIndex = allNodes.findIndex(n => n.id === node.id);
  const hasPrevious = currentNodeIndex > 0;
  const hasNext = currentNodeIndex < allNodes.length - 1;

  const handlePrevious = () => {
    if (hasPrevious && onNavigateToNode) {
      const previousNode = allNodes[currentNodeIndex - 1];
      onNavigateToNode(previousNode.id);
    }
  };

  const handleNext = () => {
    if (hasNext && onNavigateToNode) {
      const nextNode = allNodes[currentNodeIndex + 1];
      onNavigateToNode(nextNode.id);
    }
  };

  // Get node names for navigation display
  const getPreviousNodeName = () => {
    if (hasPrevious) {
      const previousNode = allNodes[currentNodeIndex - 1];
      return previousNode.data.name || `${previousNode.type.charAt(0).toUpperCase() + previousNode.type.slice(1)} Node`;
    }
    return '';
  };

  const getNextNodeName = () => {
    if (hasNext) {
      const nextNode = allNodes[currentNodeIndex + 1];
      return nextNode.data.name || `${nextNode.type.charAt(0).toUpperCase() + nextNode.type.slice(1)} Node`;
    }
    return '';
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle navigation if we have navigation capability
      if (!onNavigateToNode || allNodes.length <= 1) return;

      // Check if user is typing in an input field
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (hasPrevious) handlePrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (hasNext) handleNext();
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [hasPrevious, hasNext, handlePrevious, handleNext, onClose, onNavigateToNode, allNodes.length]);

  // Get node run data if we have a nodeRunId
  const { data: nodeRun, isLoading: isNodeRunLoading, error: nodeRunError } = useQuery<NodeRun>({
    queryKey: [`/api/node-runs/${executionState?.nodeRunId}`],
    enabled: !!executionState?.nodeRunId,
    retry: false,
  });

  // Get node logs if we have a nodeRunId
  const { data: nodeLogs, isLoading: isLogsLoading, error: logsError } = useQuery<LogEntry[]>({
    queryKey: [`/api/node-runs/${executionState?.nodeRunId}/logs`],
    enabled: !!executionState?.nodeRunId,
    retry: false,
  });

  const getNodeIcon = () => {
    switch (node.type) {
      case 'input':
        return <ArrowRight className="text-[#0078D4]" size={20} />;
      case 'api':
        return <Code className="text-[#5C2D91]" size={20} />;
      case 'api-trigger':
        return <Play className="text-[#5C2D91]" size={20} />;
      case 'prompt':
        return <MessageSquare className="text-[#FF8C00]" size={20} />;
      case 'agent':
        return <Bot className="text-[#107C10]" size={20} />;
      case 'custom':
        return <Puzzle className="text-[#D83B01]" size={20} />;
      default:
        return <Settings size={20} />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      running: 'default',
      completed: 'default',
      failed: 'destructive',
      idle: 'outline'
    } as const;

    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      running: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      completed: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      failed: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
      idle: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    };

    return (
      <Badge className={colors[status as keyof typeof colors] || colors.idle}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDuration = (startTime?: Date, endTime?: Date) => {
    if (!startTime) return 'N/A';
    const end = endTime || new Date();
    const duration = end.getTime() - new Date(startTime).getTime();
    return `${duration}ms`;
  };

  const renderJsonData = (data: any, title: string) => {
    if (!data || (typeof data === 'object' && Object.keys(data).length === 0)) {
      return (
        <div className="text-center py-8 text-gray-500">
          <AlertCircle className="mx-auto h-8 w-8 mb-2" />
          <p>No {title.toLowerCase()} data available</p>
          {title === 'Output' && status === 'running' && (
            <p className="text-xs mt-2">Execution in progress...</p>
          )}
        </div>
      );
    }

    // Handle different data types
    let displayData = data;
    let dataType = 'JSON';

    if (typeof data === 'string') {
      dataType = 'Text';
      try {
        // Try to parse as JSON for better formatting
        const parsed = JSON.parse(data);
        displayData = parsed;
        dataType = 'JSON';
      } catch {
        // Keep as string
      }
    }

    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
            Data Type: {dataType}
          </span>
          <span className="text-xs text-gray-500">
            {typeof displayData === 'string' ? `${displayData.length} chars` :
             Array.isArray(displayData) ? `${displayData.length} items` :
             typeof displayData === 'object' ? `${Object.keys(displayData).length} properties` : ''}
          </span>
        </div>
        <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg text-xs font-mono overflow-auto max-h-96 border">
          {typeof displayData === 'string' ? displayData : JSON.stringify(displayData, null, 2)}
        </pre>
      </div>
    );
  };

  const inputData = nodeRun?.input || executionState?.input;
  const outputData = nodeRun?.output || executionState?.output;
  const status = executionState?.status || 'idle';

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-xl w-[90vw] h-[80vh] max-w-7xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
          <div className="flex items-center space-x-3">
            {getNodeIcon()}
            <div>
              <h2 className="text-lg font-semibold text-neutral-800 dark:text-white">
                {node.data.name || `${node.type.charAt(0).toUpperCase() + node.type.slice(1)} Node`}
              </h2>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(status)}
                {getStatusBadge(status)}
                {executionState?.executionDuration && (
                  <span className="text-xs text-gray-500">
                    {executionState.executionDuration}ms
                  </span>
                )}
                {allNodes.length > 1 && (
                  <span className="text-xs text-gray-500">
                    Node {currentNodeIndex + 1} of {allNodes.length}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Navigation buttons */}
            {allNodes.length > 1 && onNavigateToNode && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={!hasPrevious}
                  title={hasPrevious ? `Previous: ${getPreviousNodeName()}` : 'No previous node'}
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNext}
                  disabled={!hasNext}
                  title={hasNext ? `Next: ${getNextNodeName()}` : 'No next node'}
                >
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              </>
            )}
            <Button variant="outline" size="sm" onClick={onConfigure}>
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 3-Column Layout */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Column - Input */}
          <div className="w-1/3 border-r border-neutral-200 dark:border-neutral-700 flex flex-col">
            <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
              <h3 className="font-medium text-neutral-800 dark:text-white">Input Data</h3>
            </div>
            <div className="flex-1 p-4 overflow-auto">
              {renderJsonData(inputData, 'Input')}
            </div>
          </div>

          {/* Middle Column - Configuration */}
          <div className="w-1/3 border-r border-neutral-200 dark:border-neutral-700 flex flex-col">
            <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
              <h3 className="font-medium text-neutral-800 dark:text-white">Configuration</h3>
            </div>
            <div className="flex-1 p-4 overflow-auto">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Node Type</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">{node.type}</p>
                </div>

                {node.data.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{node.data.description}</p>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Configuration</label>
                  {renderJsonData(node.data, 'Configuration')}
                </div>

                {executionState && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Execution Details</label>
                    <div className="text-xs space-y-1">
                      {executionState.startTime && (
                        <div>
                          <span className="font-medium">Started:</span>{' '}
                          {format(new Date(executionState.startTime), 'MMM d, yyyy HH:mm:ss')}
                        </div>
                      )}
                      {executionState.endTime && (
                        <div>
                          <span className="font-medium">Ended:</span>{' '}
                          {format(new Date(executionState.endTime), 'MMM d, yyyy HH:mm:ss')}
                        </div>
                      )}
                      {executionState.executionDuration && (
                        <div>
                          <span className="font-medium">Duration:</span> {executionState.executionDuration}ms
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Output */}
          <div className="w-1/3 flex flex-col">
            <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
              <h3 className="font-medium text-neutral-800 dark:text-white">Output Data</h3>
            </div>
            <div className="flex-1 p-4 overflow-auto">
              {status === 'failed' && executionState?.error ? (
                <div className="space-y-4">
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <XCircle className="w-4 h-4 text-red-500" />
                      <span className="font-medium text-red-800 dark:text-red-300">Execution Failed</span>
                    </div>
                    <p className="text-sm text-red-700 dark:text-red-400">{executionState.error}</p>
                  </div>
                  {outputData && renderJsonData(outputData, 'Output')}
                </div>
              ) : (
                renderJsonData(outputData, 'Output')
              )}
            </div>
          </div>
        </div>

        {/* Footer with logs and navigation help */}
        <div className="border-t border-neutral-200 dark:border-neutral-700">
          {nodeLogs && nodeLogs.length > 0 ? (
            <div className="p-2 max-h-32">
              <h4 className="text-sm font-medium text-neutral-800 dark:text-white mb-2">Execution Logs</h4>
              <div className="h-20 overflow-auto">
                <div className="space-y-1">
                  {nodeLogs.map((log) => (
                    <div key={log.id} className="text-xs">
                      <span className="text-gray-500">{format(new Date(log.timestamp), 'HH:mm:ss')}</span>
                      <span className={`ml-2 ${
                        log.level === 'error' ? 'text-red-600' :
                        log.level === 'warn' ? 'text-yellow-600' :
                        log.level === 'info' ? 'text-blue-600' :
                        'text-gray-600'
                      }`}>
                        [{log.level.toUpperCase()}]
                      </span>
                      <span className="ml-2">{log.message}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            allNodes.length > 1 && onNavigateToNode && (
              <div className="p-2">
                <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                  <span>💡 Use ← → arrow keys to navigate between nodes</span>
                  <span>•</span>
                  <span>Press Esc to close</span>
                </div>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default NodeExecutionView;
