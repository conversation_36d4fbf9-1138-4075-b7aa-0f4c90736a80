# API Trigger Node Test Plan

## Summary of Changes Made

### 1. Type System Updates
- Added new `api-trigger` node type to `NodeType` union
- Created `ApiTriggerNodeData` interface for webhook-specific configuration
- Updated `NodeData` union to include both `ApiNodeData` and `ApiTriggerNodeData`

### 2. Server-Side Changes
- Created `ApiTriggerNodeExecutor` class to handle webhook data processing
- Updated workflow executor to register the new node type
- Modified API trigger route to check for `api-trigger` node type instead of `api`
- Updated route logic to use `allowedMethods` array instead of single `method`

### 3. Client-Side Changes
- Updated `WorkflowBuilder` to register `api-trigger` node type
- Modified `NodePanel` to create `api-trigger` nodes instead of `api`
- Updated `ApiTriggerNode` component to display webhook-specific information
- Added comprehensive configuration UI in `NodeConfigModal` for webhook settings
- Updated `NodeProperties` to show API trigger-specific properties

### 4. Key Differences Between API Node and API Trigger Node

**API Node (`api`):**
- Makes outbound HTTP requests to external APIs
- Configured with URL, method, headers, authentication
- Used for calling external services

**API Trigger Node (`api-trigger`):**
- Receives inbound webhook requests
- Configured with allowed methods, authentication, validation
- Used as workflow entry point for external systems

## Testing the API Trigger Node

### 1. Create a Test Workflow
1. Open the workflow builder
2. Drag an "API Trigger" node onto the canvas
3. Configure it with:
   - Allowed Methods: POST
   - Authentication: None (for testing)
   - Request Validation: Enabled with method-specific schema
4. Add a subsequent node (e.g., Prompt node) to process the webhook data
5. Save the workflow

### 2. HTTP Method-Specific Validation Testing

#### GET Request Validation (Query Parameters)
```bash
# Test GET with valid query parameters
curl -X GET "http://localhost:3000/api/trigger/1/node-123?id=user123&limit=10&filter=active"

# Test GET with invalid query parameters (missing required 'id')
curl -X GET "http://localhost:3000/api/trigger/1/node-123?limit=10&filter=active"
```

**Validation Schema for GET:**
```json
{
  "type": "object",
  "properties": {
    "id": { "type": "string" },
    "limit": { "type": "number", "minimum": 1, "maximum": 100 },
    "filter": { "type": "string" }
  },
  "required": ["id"]
}
```

#### POST/PUT/PATCH Request Validation (Request Body)
```bash
# Test POST with valid request body
curl -X POST http://localhost:3000/api/trigger/1/node-123 \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "email": "<EMAIL>", "data": {"role": "admin"}}'

# Test POST with invalid request body (missing required 'email')
curl -X POST http://localhost:3000/api/trigger/1/node-123 \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "data": {"role": "admin"}}'

# Test POST with no body (falls back to query parameters)
curl -X POST "http://localhost:3000/api/trigger/1/node-123?name=John&email=<EMAIL>"
```

**Validation Schema for POST/PUT/PATCH:**
```json
{
  "type": "object",
  "properties": {
    "name": { "type": "string", "minLength": 1 },
    "email": { "type": "string", "format": "email" },
    "data": { "type": "object" }
  },
  "required": ["name", "email"]
}
```

#### DELETE Request Validation (Body or Query Parameters)
```bash
# Test DELETE with request body
curl -X DELETE http://localhost:3000/api/trigger/1/node-123 \
  -H "Content-Type: application/json" \
  -d '{"id": "user123", "force": true}'

# Test DELETE with query parameters
curl -X DELETE "http://localhost:3000/api/trigger/1/node-123?id=user123&force=true"

# Test DELETE with invalid data (missing required 'id')
curl -X DELETE http://localhost:3000/api/trigger/1/node-123 \
  -H "Content-Type: application/json" \
  -d '{"force": true}'
```

**Validation Schema for DELETE:**
```json
{
  "type": "object",
  "properties": {
    "id": { "type": "string" },
    "force": { "type": "boolean" }
  },
  "required": ["id"]
}
```

### 3. Expected Behavior
1. The API trigger node receives the webhook data
2. Validates the appropriate data based on HTTP method:
   - **GET**: Validates query parameters
   - **POST/PUT/PATCH**: Validates request body (fallback to query params)
   - **DELETE**: Validates request body or query parameters
3. Passes the structured webhook data to the next node
4. The workflow executes successfully
5. Returns a success response to the webhook caller

### 4. Enhanced Webhook Data Structure
The API trigger node now passes this enhanced data to subsequent nodes:
```json
{
  "method": "POST",
  "headers": {...},
  "query": {...},
  "body": {...},
  "params": {...},
  "timestamp": "2025-01-27T...",
  "triggerNodeId": 123,
  "validation": {
    "enabled": true,
    "validatedData": {...},
    "validationTarget": "request_body"
  }
}
```

**Validation Target Values:**
- `"query_parameters"` - for GET requests
- `"request_body"` - for POST/PUT/PATCH requests
- `"request_body_or_query_parameters"` - for DELETE requests
- `"entire_request"` - for unknown methods

## Configuration Options

### Allowed Methods
- GET, POST, PUT, PATCH, DELETE
- Multiple methods can be selected
- Defaults to POST if none selected
- Each method has color-coded accordion in UI

### Authentication
- None: Public endpoint
- API Key: Requires API key in header
- Bearer: Requires bearer token

### Rate Limiting
- Enable/disable rate limiting
- Configure requests per minute (1-1000)

### Request Validation
- Enable/disable JSON schema validation
- Method-specific validation targets
- Interactive accordion UI with examples
- Auto-expanded for selected methods

### Response Configuration
- Success status code and message
- Error status code and message
- Customizable webhook responses

## UI Features

### Method-Specific Accordion Examples
The configuration UI now shows interactive accordions for each HTTP method:

- **GET (Blue)**: Query parameter validation examples
- **POST (Green)**: Request body validation for resource creation
- **PUT (Amber)**: Request body validation for complete resource updates
- **PATCH (Purple)**: Request body validation for partial updates
- **DELETE (Red)**: Body or query parameter validation for deletion

### Auto-Expansion
- Accordions automatically expand for methods selected in "Allowed Methods"
- Manual toggle available for viewing other method examples
- Color-coded headers indicate selected vs. available methods

### Example Schemas
Each method accordion includes:
- Detailed JSON schema examples
- Property descriptions and constraints
- Usage examples and fallback behavior
- Method-specific validation rules

## Next Steps

1. Test the implementation with a real workflow
2. Verify webhook data flows correctly to subsequent nodes
3. Test authentication and validation features
4. Check rate limiting functionality
5. Ensure error handling works properly

The API trigger node should now work as a proper webhook trigger that can receive HTTP requests, validate input data, and pass the data to the next node in the workflow.
