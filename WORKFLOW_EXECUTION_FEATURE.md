# Workflow Execution History Feature

## Overview

This feature implements n8n-style workflow run history with visual indicators on nodes and a 3-column execution view, similar to how n8n displays workflow execution data.

## Features Implemented

### 1. Visual Node Indicators

Each node in the workflow canvas now displays visual execution status indicators:

- **Idle**: No indicator (default state)
- **Pending**: Yellow clock icon
- **Running**: Blue spinning loader icon
- **Completed**: Green checkmark icon
- **Failed**: Red X icon

The nodes also have colored borders and subtle shadows that match the execution status.

### 2. Enhanced Node Click Behavior

When clicking on a node:
- If the node has execution data (from current or previous runs), it opens the **3-Column Execution View**
- If no execution data exists, it opens the normal **Node Properties Panel**

### 3. 3-Column Execution View

The execution view displays:

#### Left Column - Input Data
- Shows the input data that was passed to the node
- Displays data type and size information
- Handles both JSON and text data formats

#### Middle Column - Configuration
- Node type and description
- Current node configuration
- Execution timing details (start time, end time, duration)
- Retry count and other metadata

#### Right Column - Output Data
- Shows the output data produced by the node
- For failed executions, displays error information
- Handles different output formats (JSON, text, etc.)

#### Footer - Execution Logs
- Shows detailed execution logs if available
- Color-coded by log level (error, warn, info, debug)
- Timestamped entries

### 4. Real-time Execution Tracking

- **Live Updates**: Polls for execution status every 2 seconds during active runs
- **Node-level Tracking**: Tracks individual node execution states
- **Workflow-level Status**: Shows overall workflow execution status
- **Automatic Notifications**: Toast notifications when workflows complete or fail

### 5. Execution Status Panel

A status panel in the top-right corner shows:
- Current workflow execution status
- Workflow run ID
- Prevents multiple simultaneous runs

## Technical Implementation

### Components Created/Modified

1. **NodeExecutionView.tsx** - New 3-column execution view component
2. **useWorkflowExecution.ts** - Hook for managing execution state
3. **BaseNode.tsx** - Enhanced with execution status indicators
4. **WorkflowBuilder.tsx** - Updated with execution state management
5. **All Node Components** - Updated to pass execution status
6. **RunWorkflowModal.tsx** - Modified to return workflow run ID

### Data Flow

1. User runs a workflow → `RunWorkflowModal` creates workflow run
2. `useWorkflowExecution` hook starts tracking the run
3. Hook polls for node run updates every 2 seconds
4. Node execution states are updated in real-time
5. Visual indicators on nodes reflect current status
6. Clicking nodes shows execution data in 3-column view

### API Integration

The feature integrates with existing API endpoints:
- `/api/workflow-runs` - Get workflow runs
- `/api/workflow-runs/:runId/node-runs` - Get node runs for a workflow run
- `/api/node-runs/:nodeRunId/logs` - Get logs for a specific node run

## Usage

### Running a Workflow

1. Open a workflow in the editor
2. Click "Run Workflow" button
3. Fill in input parameters (if required)
4. Click "Run Workflow" to start execution
5. Watch real-time visual indicators on nodes
6. Click any node to see detailed execution data

### Viewing Execution History

1. Open a workflow that has been executed before
2. The latest execution state is automatically loaded
3. Click any node to see its execution data
4. Use the 3-column view to inspect input, configuration, and output

### Understanding Visual Indicators

- **No indicator**: Node hasn't been executed yet
- **Yellow clock**: Node is queued for execution
- **Blue spinner**: Node is currently executing
- **Green checkmark**: Node completed successfully
- **Red X**: Node failed during execution

## Benefits

1. **Immediate Visual Feedback**: See execution status at a glance
2. **Detailed Debugging**: Inspect input/output data for each node
3. **Real-time Monitoring**: Watch workflows execute in real-time
4. **Historical Analysis**: Review past execution data
5. **Error Diagnosis**: Quickly identify and debug failed nodes

## Future Enhancements

Potential improvements that could be added:
- Execution time visualization (progress bars)
- Node performance metrics
- Execution branching visualization
- Export execution data
- Execution comparison between runs
- Advanced filtering and search in execution history
